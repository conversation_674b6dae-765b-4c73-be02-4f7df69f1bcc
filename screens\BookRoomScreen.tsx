import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  FlatList,
  Modal
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { toast } from 'sonner-native';

// Sample meeting room data
const ROOMS = [
  {
    id: '1',
    name: '<PERSON>',
    floor: '3rd',
    capacity: 8,
    hasAV: true,
    hasWhiteboard: true,
    hasTeleconference: true,
  },
  {
    id: '2',
    name: '<PERSON>',
    floor: '2nd',
    capacity: 12,
    hasAV: true,
    hasWhiteboard: true,
    hasTeleconference: true,
  },
  {
    id: '3',
    name: '<PERSON>',
    floor: '2nd',
    capacity: 6,
    hasAV: true,
    hasWhiteboard: false,
    hasTeleconference: false,
  },
  {
    id: '4',
    name: 'Sparrow',
    floor: '1st',
    capacity: 4,
    hasAV: false,
    hasWhiteboard: true,
    hasTeleconference: false,
  },
  {
    id: '5',
    name: 'Condor',
    floor: '4th',
    capacity: 20,
    hasAV: true,
    hasWhiteboard: true,
    hasTeleconference: true,
  },
];

// Sample time slots
const TIME_SLOTS = [
  '08:00 AM', '09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
  '01:00 PM', '02:00 PM', '03:00 PM', '04:00 PM', '05:00 PM'
];

export default function BookRoomScreen() {
  const navigation = useNavigation();
  
  // State
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [duration, setDuration] = useState(1);
  const [purpose, setPurpose] = useState('');
  const [filteredRooms, setFilteredRooms] = useState(ROOMS);
  const [modalVisible, setModalVisible] = useState(false);
  const [filterCapacity, setFilterCapacity] = useState(0);
  const [filterFloor, setFilterFloor] = useState('All');
  const [filterAV, setFilterAV] = useState(false);
  
  // Generate dates for the next 7 days
  const getDates = () => {
    const dates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      dates.push(date);
    }
    return dates;
  };
  
  const dates = getDates();
  
  // Format date for display
  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };
  
  // Get day name
  const getDayName = (date) => {
    return date.toLocaleDateString('en-US', { weekday: 'short' });
  };
  
  // Is date selected
  const isDateSelected = (date) => {
    return date.toDateString() === selectedDate.toDateString();
  };
  
  // Handle room selection
  const handleRoomSelection = (room) => {
    setSelectedRoom(room);
  };
  
  // Handle time slot selection
  const handleTimeSlotSelection = (timeSlot) => {
    setSelectedTimeSlot(timeSlot);
  };
  
  // Apply filters
  const applyFilters = () => {
    let results = [...ROOMS];
    
    if (filterCapacity > 0) {
      results = results.filter(room => room.capacity >= filterCapacity);
    }
    
    if (filterFloor !== 'All') {
      results = results.filter(room => room.floor === filterFloor);
    }
    
    if (filterAV) {
      results = results.filter(room => room.hasAV);
    }
    
    setFilteredRooms(results);
    setModalVisible(false);
  };
  
  // Reset filters
  const resetFilters = () => {
    setFilterCapacity(0);
    setFilterFloor('All');
    setFilterAV(false);
    setFilteredRooms(ROOMS);
    setModalVisible(false);
  };
  
  // Book room
  const bookRoom = () => {
    if (!selectedRoom || !selectedTimeSlot) {
      toast.error('Please select a room and time slot');
      return;
    }
    
    if (!purpose.trim()) {
      toast.error('Please enter a purpose for booking');
      return;
    }
    
    // In a real app, this would make an API call to book the room
    toast.success(`Room ${selectedRoom.name} booked successfully for ${selectedTimeSlot}!`);
    
    // In a real app, this would navigate back to home or to a confirmation screen
    navigation.goBack();
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#222B45" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Book a Room</Text>
        <TouchableOpacity onPress={() => setModalVisible(true)} style={styles.filterButton}>
          <Ionicons name="options-outline" size={24} color="#4A80F0" />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Date Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Date</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.dateScrollView}>
            {dates.map((date, index) => (
              <TouchableOpacity 
                key={index} 
                style={[
                  styles.dateItem,
                  isDateSelected(date) && styles.selectedDateItem
                ]}
                onPress={() => setSelectedDate(date)}
              >
                <Text style={[
                  styles.dayName,
                  isDateSelected(date) && styles.selectedDateText
                ]}>
                  {getDayName(date)}
                </Text>
                <Text style={[
                  styles.dateNumber,
                  isDateSelected(date) && styles.selectedDateText
                ]}>
                  {date.getDate()}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        {/* Room Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Available Rooms</Text>
          {filteredRooms.length === 0 ? (
            <View style={styles.noRoomsContainer}>
              <Ionicons name="alert-circle-outline" size={48} color="#8F9BB3" />
              <Text style={styles.noRoomsText}>No rooms match your filters</Text>
              <TouchableOpacity 
                style={styles.resetButton} 
                onPress={resetFilters}
              >
                <Text style={styles.resetButtonText}>Reset Filters</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={filteredRooms}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
              renderItem={({ item }) => (
                <TouchableOpacity 
                  style={[
                    styles.roomCard,
                    selectedRoom?.id === item.id && styles.selectedRoomCard
                  ]}
                  onPress={() => handleRoomSelection(item)}
                >
                  <View style={styles.roomInfo}>
                    <Text style={styles.roomName}>{item.name}</Text>
                    <Text style={styles.roomDetails}>{item.floor} Floor • Capacity: {item.capacity}</Text>
                    <View style={styles.amenitiesContainer}>
                      {item.hasAV && (
                        <View style={styles.amenityTag}>
                          <Ionicons name="tv-outline" size={14} color="#4A80F0" />
                          <Text style={styles.amenityText}>AV</Text>
                        </View>
                      )}
                      {item.hasWhiteboard && (
                        <View style={styles.amenityTag}>
                          <Ionicons name="document-text-outline" size={14} color="#4A80F0" />
                          <Text style={styles.amenityText}>Whiteboard</Text>
                        </View>
                      )}
                      {item.hasTeleconference && (
                        <View style={styles.amenityTag}>
                          <Ionicons name="videocam-outline" size={14} color="#4A80F0" />
                          <Text style={styles.amenityText}>Teleconf</Text>
                        </View>
                      )}
                    </View>
                  </View>
                  <View style={styles.roomSelection}>
                    {selectedRoom?.id === item.id ? (
                      <Ionicons name="checkmark-circle" size={24} color="#4A80F0" />
                    ) : (
                      <Ionicons name="ellipse-outline" size={24} color="#8F9BB3" />
                    )}
                  </View>
                </TouchableOpacity>
              )}
            />
          )}
        </View>
        
        {/* Time Slot Selection */}
        {selectedRoom && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Select Time</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.timeScrollView}>
              {TIME_SLOTS.map((timeSlot, index) => (
                <TouchableOpacity 
                  key={index} 
                  style={[
                    styles.timeSlot,
                    selectedTimeSlot === timeSlot && styles.selectedTimeSlot
                  ]}
                  onPress={() => handleTimeSlotSelection(timeSlot)}
                >
                  <Text style={[
                    styles.timeSlotText,
                    selectedTimeSlot === timeSlot && styles.selectedTimeSlotText
                  ]}>
                    {timeSlot}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
        
        {/* Duration Selection */}
        {selectedTimeSlot && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Duration (hours)</Text>
            <View style={styles.durationContainer}>
              <TouchableOpacity 
                style={styles.durationControl}
                onPress={() => duration > 1 && setDuration(duration - 1)}
                disabled={duration <= 1}
              >
                <Ionicons name="remove" size={24} color={duration <= 1 ? "#D7D9E0" : "#4A80F0"} />
              </TouchableOpacity>
              <Text style={styles.durationText}>{duration}</Text>
              <TouchableOpacity 
                style={styles.durationControl}
                onPress={() => duration < 4 && setDuration(duration + 1)}
                disabled={duration >= 4}
              >
                <Ionicons name="add" size={24} color={duration >= 4 ? "#D7D9E0" : "#4A80F0"} />
              </TouchableOpacity>
            </View>
          </View>
        )}
        
        {/* Booking Purpose */}
        {selectedTimeSlot && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Purpose</Text>
            <TextInput
              style={styles.purposeInput}
              placeholder="e.g., Team Meeting, Client Call, etc."
              value={purpose}
              onChangeText={setPurpose}
              multiline
            />
          </View>
        )}
        
        {/* Book Button */}
        {selectedRoom && selectedTimeSlot && (
          <TouchableOpacity 
            style={styles.bookButton}
            onPress={bookRoom}
          >
            <Text style={styles.bookButtonText}>Book Room</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
      
      {/* Filter Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Rooms</Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons name="close" size={24} color="#222B45" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.filterSection}>
              <Text style={styles.filterLabel}>Minimum Capacity</Text>
              <TextInput
                style={styles.filterInput}
                keyboardType="numeric"
                placeholder="Enter minimum capacity"
                value={filterCapacity.toString() === '0' ? '' : filterCapacity.toString()}
                onChangeText={(text) => {
                  const number = parseInt(text) || 0;
                  setFilterCapacity(number);
                }}
              />
            </View>
            
            <View style={styles.filterSection}>
              <Text style={styles.filterLabel}>Floor</Text>
              <View style={styles.filterOptions}>
                {['All', '1st', '2nd', '3rd', '4th'].map((floor) => (
                  <TouchableOpacity
                    key={floor}
                    style={[
                      styles.filterOption,
                      filterFloor === floor && styles.selectedFilterOption
                    ]}
                    onPress={() => setFilterFloor(floor)}
                  >
                    <Text
                      style={[
                        styles.filterOptionText,
                        filterFloor === floor && styles.selectedFilterOptionText
                      ]}
                    >
                      {floor}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <View style={styles.filterSection}>
              <Text style={styles.filterLabel}>Amenities</Text>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setFilterAV(!filterAV)}
              >
                <View style={[styles.checkbox, filterAV && styles.checkedCheckbox]}>
                  {filterAV && <Ionicons name="checkmark" size={16} color="#FFFFFF" />}
                </View>
                <Text style={styles.checkboxLabel}>Requires AV Equipment</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalActions}>
              <TouchableOpacity style={styles.resetFilterButton} onPress={resetFilters}>
                <Text style={styles.resetFilterButtonText}>Reset</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.applyFilterButton} onPress={applyFilters}>
                <Text style={styles.applyFilterButtonText}>Apply</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FD',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EDF1F7',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222B45',
  },
  filterButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222B45',
    marginBottom: 16,
  },
  dateScrollView: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  dateItem: {
    width: 60,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedDateItem: {
    backgroundColor: '#4A80F0',
  },
  dayName: {
    fontSize: 14,
    color: '#8F9BB3',
    marginBottom: 4,
  },
  dateNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222B45',
  },
  selectedDateText: {
    color: 'white',
  },
  roomCard: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedRoomCard: {
    borderColor: '#4A80F0',
    borderWidth: 2,
  },
  roomInfo: {
    flex: 1,
  },
  roomName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222B45',
    marginBottom: 4,
  },
  roomDetails: {
    fontSize: 14,
    color: '#8F9BB3',
    marginBottom: 8,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  amenityTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F7FF',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 4,
  },
  amenityText: {
    fontSize: 12,
    color: '#4A80F0',
    marginLeft: 4,
  },
  roomSelection: {
    justifyContent: 'center',
  },
  timeScrollView: {
    flexDirection: 'row',
  },
  timeSlot: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderRadius: 8,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedTimeSlot: {
    backgroundColor: '#4A80F0',
  },
  timeSlotText: {
    color: '#222B45',
    fontWeight: '500',
  },
  selectedTimeSlotText: {
    color: 'white',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  durationControl: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FD',
    borderRadius: 20,
  },
  durationText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#222B45',
    marginHorizontal: 24,
  },
  purposeInput: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    textAlignVertical: 'top',
    minHeight: 100,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  bookButton: {
    backgroundColor: '#4A80F0',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  bookButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    minHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222B45',
  },
  filterSection: {
    marginBottom: 24,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#222B45',
    marginBottom: 12,
  },
  filterInput: {
    backgroundColor: '#F7F9FC',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#EDF1F7',
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F7F9FC',
    marginRight: 10,
    marginBottom: 10,
  },
  selectedFilterOption: {
    backgroundColor: '#4A80F0',
  },
  filterOptionText: {
    color: '#222B45',
  },
  selectedFilterOptionText: {
    color: 'white',
    fontWeight: '600',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#8F9BB3',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedCheckbox: {
    backgroundColor: '#4A80F0',
    borderColor: '#4A80F0',
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#222B45',
  },
  modalActions: {
    flexDirection: 'row',
    marginTop: 24,
  },
  resetFilterButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#F7F9FC',
    alignItems: 'center',
    marginRight: 12,
  },
  resetFilterButtonText: {
    color: '#8F9BB3',
    fontSize: 16,
    fontWeight: '600',
  },
  applyFilterButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#4A80F0',
    alignItems: 'center',
  },
  applyFilterButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  noRoomsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noRoomsText: {
    fontSize: 16,
    color: '#8F9BB3',
    marginVertical: 16,
  },
  resetButton: {
    backgroundColor: '#4A80F0',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  resetButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});